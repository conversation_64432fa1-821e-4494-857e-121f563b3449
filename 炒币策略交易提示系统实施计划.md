# 炒币策略交易提示系统实施计划

## 项目概述

### 项目目标
开发一款基于Python后端和HTML前端的炒币策略交易提示系统，集成欧意(OKX) API，提供技术分析、市场情绪分析、交易信号生成、策略回测和模拟交易功能，最终打包为桌面可执行程序。

### 核心功能
- 实时行情数据获取和展示
- 多种技术指标分析和信号生成
- 策略回测和性能评估
- 模拟交易和风险管理
- 直观的图表界面和交易面板

## 市场调研和竞品分析

### 主要竞品
1. **TradingView**
   - 优势：功能强大、Pine Script策略编写、社区活跃
   - 劣势：付费订阅、无法直接交易、依赖网络

2. **TrendSpider**
   - 优势：自动化技术分析、多时间框架分析
   - 劣势：价格昂贵、主要面向股票市场

3. **Freqtrade**
   - 优势：开源、支持多交易所、策略丰富
   - 劣势：配置复杂、需要技术背景、界面简陋

### 我们的差异化优势
- 专注于加密货币市场
- 本地化部署，数据安全
- 简洁直观的桌面应用界面
- 集成策略开发和回测功能
- 一键部署，无需复杂配置

## 技术架构设计

### 推荐技术栈

#### 后端技术
- **Python 3.9+**: 主要开发语言
- **CCXT**: 统一的加密货币交易所API库
- **pandas-ta**: 技术分析指标库(150+指标)
- **FastAPI**: 高性能Web框架
- **SQLite**: 轻量级本地数据库
- **asyncio**: 异步编程支持

#### 前端技术
- **NiceGUI**: Python原生GUI框架(推荐方案)
- **Plotly**: 交互式图表库
- **HTML/CSS/JavaScript**: 传统Web技术(备选方案)

#### 桌面打包
- **PyInstaller**: Python应用打包工具
- **nicegui-pack**: NiceGUI专用打包工具

#### 回测引擎
- **backtesting.py**: 轻量级回测框架(推荐)
- **vectorbt**: 高性能向量化回测(备选)

### 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    用户界面层 (NiceGUI)                      │
├─────────────────────────────────────────────────────────────┤
│  实时图表  │  策略面板  │  回测结果  │  交易信号  │  设置面板  │
├─────────────────────────────────────────────────────────────┤
│                    业务逻辑层 (FastAPI)                      │
├─────────────────────────────────────────────────────────────┤
│  策略引擎  │  信号生成  │  风险管理  │  回测引擎  │  模拟交易  │
├─────────────────────────────────────────────────────────────┤
│                    数据处理层 (pandas-ta)                    │
├─────────────────────────────────────────────────────────────┤
│  技术指标  │  数据清洗  │  特征工程  │  信号过滤  │  性能计算  │
├─────────────────────────────────────────────────────────────┤
│                    数据获取层 (CCXT)                         │
├─────────────────────────────────────────────────────────────┤
│  OKX API   │  实时行情  │  历史数据  │  账户信息  │  订单管理  │
├─────────────────────────────────────────────────────────────┤
│                    数据存储层 (SQLite)                       │
├─────────────────────────────────────────────────────────────┤
│  行情数据  │  策略配置  │  回测结果  │  交易记录  │  用户设置  │
└─────────────────────────────────────────────────────────────┘
```

## 核心模块设计

### 1. 数据获取模块 (DataProvider)
```python
# 主要功能
- OKX API集成和认证
- 实时行情数据获取
- 历史K线数据下载
- WebSocket连接管理
- 数据缓存和持久化

# 核心类
class OKXDataProvider:
    def __init__(self, api_key, secret, passphrase)
    async def get_realtime_ticker(self, symbol)
    async def get_historical_klines(self, symbol, timeframe, limit)
    async def subscribe_ticker(self, symbol, callback)
```

### 2. 技术分析模块 (TechnicalAnalysis)
```python
# 主要功能
- 150+技术指标计算
- 自定义指标组合
- 信号生成和过滤
- 多时间框架分析

# 支持的指标类别
- 趋势指标: SMA, EMA, MACD, ADX, Aroon
- 动量指标: RSI, Stochastic, Williams %R
- 波动性指标: Bollinger Bands, ATR, Keltner Channel
- 成交量指标: OBV, MFI, VWAP
```

### 3. 策略引擎模块 (StrategyEngine)
```python
# 主要功能
- 策略定义和管理
- 信号生成和验证
- 风险控制和仓位管理
- 多策略组合

# 内置策略示例
- 金叉死叉策略
- RSI超买超卖策略
- 布林带突破策略
- 多指标组合策略
```

### 4. 回测引擎模块 (BacktestEngine)
```python
# 主要功能
- 历史数据回测
- 性能指标计算
- 风险分析报告
- 参数优化

# 性能指标
- 总收益率、年化收益率
- 最大回撤、夏普比率
- 胜率、盈亏比
- 交易次数、持仓时间
```

### 5. 模拟交易模块 (PaperTrading)
```python
# 主要功能
- 虚拟账户管理
- 模拟订单执行
- 实时盈亏计算
- 交易记录管理

# 订单类型支持
- 市价单、限价单
- 止损单、止盈单
- 条件单、计划委托
```

### 6. 用户界面模块 (UserInterface)
```python
# 主要功能
- 实时图表展示
- 策略配置面板
- 交易信号提示
- 回测结果展示
- 系统设置管理

# 界面组件
- K线图表 (Plotly)
- 指标叠加显示
- 信号标记和提示
- 数据表格展示
```

## 开发计划和时间线

### 第一阶段：基础架构搭建 (2-3周)
- [x] 项目结构设计
- [ ] 开发环境配置
- [ ] CCXT集成和OKX API测试
- [ ] 基础数据获取功能
- [ ] SQLite数据库设计
- [ ] NiceGUI界面框架搭建

### 第二阶段：核心功能开发 (4-5周)
- [ ] 技术指标计算模块
- [ ] 策略引擎基础框架
- [ ] 实时数据展示界面
- [ ] 基础图表功能
- [ ] 简单策略实现

### 第三阶段：高级功能开发 (3-4周)
- [ ] 回测引擎开发
- [ ] 模拟交易功能
- [ ] 策略优化工具
- [ ] 风险管理模块
- [ ] 高级图表功能

### 第四阶段：界面优化和测试 (2-3周)
- [ ] 用户界面优化
- [ ] 性能优化
- [ ] 功能测试
- [ ] 用户体验改进
- [ ] 文档编写

### 第五阶段：打包和部署 (1-2周)
- [ ] PyInstaller打包配置
- [ ] 多平台兼容性测试
- [ ] 安装程序制作
- [ ] 用户手册编写
- [ ] 发布准备

## 技术实现细节

### OKX API集成
```python
import ccxt

# OKX交易所初始化
exchange = ccxt.okx({
    'apiKey': 'your_api_key',
    'secret': 'your_secret',
    'password': 'your_passphrase',
    'sandbox': False,  # 生产环境设为False
    'enableRateLimit': True,
})

# 获取实时行情
ticker = await exchange.fetch_ticker('BTC/USDT')
# 获取K线数据
ohlcv = await exchange.fetch_ohlcv('BTC/USDT', '1h', limit=100)
```

### 技术指标计算
```python
import pandas_ta as ta

# 计算技术指标
df['SMA_20'] = ta.sma(df['close'], length=20)
df['RSI'] = ta.rsi(df['close'], length=14)
df['MACD'] = ta.macd(df['close'])['MACD_12_26_9']
df['BB_upper'], df['BB_middle'], df['BB_lower'] = ta.bbands(df['close'], length=20)
```

### 策略信号生成
```python
def generate_signals(df):
    signals = pd.DataFrame(index=df.index)
    
    # 金叉信号
    signals['golden_cross'] = (df['SMA_10'] > df['SMA_20']) & (df['SMA_10'].shift(1) <= df['SMA_20'].shift(1))
    
    # RSI超卖信号
    signals['rsi_oversold'] = (df['RSI'] < 30) & (df['RSI'].shift(1) >= 30)
    
    # 布林带突破信号
    signals['bb_breakout'] = df['close'] > df['BB_upper']
    
    return signals
```

### NiceGUI界面开发
```python
from nicegui import ui, app
import plotly.graph_objects as go

@ui.page('/')
def main_page():
    with ui.row():
        with ui.column():
            ui.label('炒币策略交易系统').classes('text-h4')
            
            # 图表区域
            chart = ui.plotly(go.Figure()).classes('w-full h-96')
            
            # 控制面板
            with ui.card():
                ui.label('策略配置')
                symbol_select = ui.select(['BTC/USDT', 'ETH/USDT'], value='BTC/USDT')
                timeframe_select = ui.select(['1m', '5m', '15m', '1h'], value='1h')
                ui.button('开始分析', on_click=lambda: start_analysis())

if __name__ == '__main__':
    ui.run(title='炒币策略交易系统', port=8080)
```

## 技术风险和解决方案

### 1. API限制和稳定性
**风险**: OKX API调用频率限制、网络不稳定
**解决方案**: 
- 实现智能限频机制
- 添加重试和错误处理
- 本地数据缓存策略

### 2. 数据准确性和延迟
**风险**: 实时数据延迟、数据质量问题
**解决方案**:
- 多数据源验证
- 数据质量检查
- WebSocket实时连接

### 3. 策略过拟合
**风险**: 回测结果过于乐观、实盘表现差
**解决方案**:
- 样本外测试
- 滑动窗口验证
- 多市场环境测试

### 4. 性能优化
**风险**: 大量数据处理导致界面卡顿
**解决方案**:
- 异步数据处理
- 数据分页加载
- 后台计算线程

## 部署和维护计划

### 打包配置
```python
# pyinstaller配置文件 (main.spec)
a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[('templates', 'templates'), ('static', 'static')],
    hiddenimports=['pandas_ta', 'ccxt', 'nicegui'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)
```

### 系统要求
- **操作系统**: Windows 10+, macOS 10.14+, Ubuntu 18.04+
- **内存**: 最低4GB，推荐8GB
- **存储**: 最低2GB可用空间
- **网络**: 稳定的互联网连接

### 维护计划
- 定期更新技术指标库
- OKX API变更适配
- 性能优化和bug修复
- 新功能开发和用户反馈处理

## 预算估算

### 开发成本
- 开发时间: 12-17周
- 人力成本: 1名全栈开发工程师
- 第三方服务: OKX API (免费)
- 开发工具: 开源免费

### 运营成本
- 服务器: 无需(本地部署)
- API费用: 无(OKX免费API)
- 维护成本: 低

## 下一步行动

1. **需求确认**: 与您确认具体功能需求和优先级
2. **技术选型**: 最终确定前端技术方案
3. **原型开发**: 快速开发MVP版本
4. **迭代优化**: 根据使用反馈持续改进

---

## 详细技术实现示例

### 完整的策略类实现
```python
import pandas as pd
import pandas_ta as ta
from typing import Dict, List, Optional
from dataclasses import dataclass

@dataclass
class Signal:
    timestamp: pd.Timestamp
    symbol: str
    signal_type: str  # 'BUY', 'SELL', 'HOLD'
    price: float
    confidence: float
    indicators: Dict[str, float]
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None

class TradingStrategy:
    def __init__(self, name: str, parameters: Dict):
        self.name = name
        self.parameters = parameters
        self.signals = []

    def calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        # 移动平均线
        df['SMA_10'] = ta.sma(df['close'], length=10)
        df['SMA_20'] = ta.sma(df['close'], length=20)
        df['EMA_12'] = ta.ema(df['close'], length=12)

        # 动量指标
        df['RSI'] = ta.rsi(df['close'], length=14)
        macd = ta.macd(df['close'])
        df['MACD'] = macd['MACD_12_26_9']
        df['MACD_signal'] = macd['MACDs_12_26_9']

        # 波动性指标
        bbands = ta.bbands(df['close'], length=20)
        df['BB_upper'] = bbands['BBU_20_2.0']
        df['BB_middle'] = bbands['BBM_20_2.0']
        df['BB_lower'] = bbands['BBL_20_2.0']

        # 成交量指标
        df['OBV'] = ta.obv(df['close'], df['volume'])
        df['VWAP'] = ta.vwap(df['high'], df['low'], df['close'], df['volume'])

        return df

    def generate_signals(self, df: pd.DataFrame) -> List[Signal]:
        """生成交易信号"""
        signals = []

        for i in range(1, len(df)):
            current = df.iloc[i]
            previous = df.iloc[i-1]

            # 金叉信号
            if (current['SMA_10'] > current['SMA_20'] and
                previous['SMA_10'] <= previous['SMA_20']):

                confidence = self._calculate_confidence(current)
                if confidence > 0.6:
                    signal = Signal(
                        timestamp=current.name,
                        symbol=current.get('symbol', 'BTC/USDT'),
                        signal_type='BUY',
                        price=current['close'],
                        confidence=confidence,
                        indicators={
                            'RSI': current['RSI'],
                            'MACD': current['MACD'],
                            'BB_position': (current['close'] - current['BB_lower']) /
                                         (current['BB_upper'] - current['BB_lower'])
                        },
                        stop_loss=current['close'] * 0.95,
                        take_profit=current['close'] * 1.1
                    )
                    signals.append(signal)

        return signals

    def _calculate_confidence(self, row: pd.Series) -> float:
        """计算信号置信度"""
        confidence = 0.5

        # RSI确认
        if 30 < row['RSI'] < 70:
            confidence += 0.2

        # MACD确认
        if row['MACD'] > row['MACD_signal']:
            confidence += 0.2

        # 成交量确认
        if row['volume'] > row.get('volume_sma_20', 0):
            confidence += 0.1

        return min(confidence, 1.0)

# 使用示例
strategy = TradingStrategy("多指标组合策略", {
    'sma_short': 10,
    'sma_long': 20,
    'rsi_period': 14,
    'confidence_threshold': 0.6
})
```

### 实时数据处理系统
```python
import asyncio
import ccxt.pro as ccxtpro
from datetime import datetime
import sqlite3
from typing import Callable

class RealTimeDataManager:
    def __init__(self, exchange_id: str = 'okx'):
        self.exchange = getattr(ccxtpro, exchange_id)()
        self.subscribers = {}
        self.db_connection = sqlite3.connect('trading_data.db', check_same_thread=False)
        self.setup_database()

    def setup_database(self):
        """初始化数据库表"""
        cursor = self.db_connection.cursor()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS tickers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT,
                timestamp DATETIME,
                open REAL,
                high REAL,
                low REAL,
                close REAL,
                volume REAL
            )
        ''')
        self.db_connection.commit()

    async def subscribe_ticker(self, symbol: str, callback: Callable):
        """订阅实时行情"""
        if symbol not in self.subscribers:
            self.subscribers[symbol] = []
        self.subscribers[symbol].append(callback)

        # 启动WebSocket连接
        asyncio.create_task(self._watch_ticker(symbol))

    async def _watch_ticker(self, symbol: str):
        """监听实时行情数据"""
        while True:
            try:
                ticker = await self.exchange.watch_ticker(symbol)

                # 保存到数据库
                self._save_ticker_to_db(symbol, ticker)

                # 通知所有订阅者
                for callback in self.subscribers.get(symbol, []):
                    await callback(ticker)

            except Exception as e:
                print(f"Error watching ticker {symbol}: {e}")
                await asyncio.sleep(5)  # 重连延迟

    def _save_ticker_to_db(self, symbol: str, ticker: dict):
        """保存行情数据到数据库"""
        cursor = self.db_connection.cursor()
        cursor.execute('''
            INSERT INTO tickers (symbol, timestamp, open, high, low, close, volume)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            symbol,
            datetime.fromtimestamp(ticker['timestamp'] / 1000),
            ticker['open'],
            ticker['high'],
            ticker['low'],
            ticker['last'],
            ticker['baseVolume']
        ))
        self.db_connection.commit()

    async def get_historical_data(self, symbol: str, timeframe: str, limit: int = 100):
        """获取历史数据"""
        ohlcv = await self.exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
        df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        df.set_index('timestamp', inplace=True)
        return df
```

### NiceGUI高级界面组件
```python
from nicegui import ui, app, run
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import asyncio

class TradingDashboard:
    def __init__(self):
        self.data_manager = RealTimeDataManager()
        self.strategy = TradingStrategy("默认策略", {})
        self.current_symbol = 'BTC/USDT'
        self.chart = None
        self.signals_table = None

    def create_main_layout(self):
        """创建主界面布局"""
        with ui.header():
            ui.label('炒币策略交易提示系统').classes('text-h5 text-white')

        with ui.left_drawer(top_corner=True, bottom_corner=True).style('background-color: #f5f5f5'):
            self.create_control_panel()

        with ui.page_sticky(position='bottom-right', x_offset=20, y_offset=20):
            ui.button('实时监控', on_click=self.toggle_monitoring).props('fab color=primary')

    def create_control_panel(self):
        """创建控制面板"""
        with ui.card().classes('w-full'):
            ui.label('交易对选择').classes('text-h6')
            self.symbol_select = ui.select(
                ['BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT'],
                value='BTC/USDT',
                on_change=self.on_symbol_change
            ).classes('w-full')

            ui.label('时间周期').classes('text-h6 mt-4')
            self.timeframe_select = ui.select(
                ['1m', '5m', '15m', '30m', '1h', '4h', '1d'],
                value='1h',
                on_change=self.on_timeframe_change
            ).classes('w-full')

            ui.separator()

            ui.label('策略参数').classes('text-h6')
            with ui.row():
                self.sma_short = ui.number('短期均线', value=10, min=5, max=50)
                self.sma_long = ui.number('长期均线', value=20, min=10, max=100)

            with ui.row():
                self.rsi_period = ui.number('RSI周期', value=14, min=5, max=30)
                self.confidence_threshold = ui.slider(min=0.5, max=1.0, value=0.7, step=0.1)

            ui.button('应用策略', on_click=self.apply_strategy).classes('w-full mt-4')

        with ui.card().classes('w-full mt-4'):
            ui.label('实时信号').classes('text-h6')
            self.signals_table = ui.table(
                columns=[
                    {'name': 'time', 'label': '时间', 'field': 'time'},
                    {'name': 'signal', 'label': '信号', 'field': 'signal'},
                    {'name': 'price', 'label': '价格', 'field': 'price'},
                    {'name': 'confidence', 'label': '置信度', 'field': 'confidence'},
                ],
                rows=[]
            ).classes('w-full')

    def create_chart_area(self):
        """创建图表区域"""
        with ui.card().classes('w-full h-96'):
            ui.label('价格图表').classes('text-h6')
            self.chart = ui.plotly(self.create_empty_chart()).classes('w-full h-full')

    def create_empty_chart(self):
        """创建空图表"""
        fig = make_subplots(
            rows=3, cols=1,
            shared_xaxes=True,
            vertical_spacing=0.05,
            subplot_titles=('价格', 'RSI', '成交量'),
            row_heights=[0.6, 0.2, 0.2]
        )

        fig.update_layout(
            title='实时行情图表',
            xaxis_title='时间',
            yaxis_title='价格',
            template='plotly_white',
            showlegend=True
        )

        return fig

    async def update_chart(self, df: pd.DataFrame):
        """更新图表数据"""
        if self.chart is None:
            return

        # 计算技术指标
        df = self.strategy.calculate_indicators(df)

        # 创建K线图
        fig = make_subplots(
            rows=3, cols=1,
            shared_xaxes=True,
            vertical_spacing=0.05,
            subplot_titles=('价格与指标', 'RSI', '成交量'),
            row_heights=[0.6, 0.2, 0.2]
        )

        # K线图
        fig.add_trace(
            go.Candlestick(
                x=df.index,
                open=df['open'],
                high=df['high'],
                low=df['low'],
                close=df['close'],
                name='价格'
            ),
            row=1, col=1
        )

        # 移动平均线
        fig.add_trace(
            go.Scatter(x=df.index, y=df['SMA_10'], name='SMA 10', line=dict(color='blue')),
            row=1, col=1
        )
        fig.add_trace(
            go.Scatter(x=df.index, y=df['SMA_20'], name='SMA 20', line=dict(color='red')),
            row=1, col=1
        )

        # 布林带
        fig.add_trace(
            go.Scatter(x=df.index, y=df['BB_upper'], name='BB Upper', line=dict(color='gray', dash='dash')),
            row=1, col=1
        )
        fig.add_trace(
            go.Scatter(x=df.index, y=df['BB_lower'], name='BB Lower', line=dict(color='gray', dash='dash')),
            row=1, col=1
        )

        # RSI
        fig.add_trace(
            go.Scatter(x=df.index, y=df['RSI'], name='RSI', line=dict(color='purple')),
            row=2, col=1
        )
        fig.add_hline(y=70, line_dash="dash", line_color="red", row=2, col=1)
        fig.add_hline(y=30, line_dash="dash", line_color="green", row=2, col=1)

        # 成交量
        fig.add_trace(
            go.Bar(x=df.index, y=df['volume'], name='成交量', marker_color='lightblue'),
            row=3, col=1
        )

        fig.update_layout(
            title=f'{self.current_symbol} 实时行情分析',
            xaxis_title='时间',
            template='plotly_white',
            showlegend=True,
            height=800
        )

        self.chart.figure = fig

    async def on_symbol_change(self):
        """交易对变更处理"""
        self.current_symbol = self.symbol_select.value
        await self.refresh_data()

    async def on_timeframe_change(self):
        """时间周期变更处理"""
        await self.refresh_data()

    async def apply_strategy(self):
        """应用策略参数"""
        self.strategy.parameters.update({
            'sma_short': self.sma_short.value,
            'sma_long': self.sma_long.value,
            'rsi_period': self.rsi_period.value,
            'confidence_threshold': self.confidence_threshold.value
        })
        await self.refresh_data()

    async def refresh_data(self):
        """刷新数据和图表"""
        try:
            df = await self.data_manager.get_historical_data(
                self.current_symbol,
                self.timeframe_select.value,
                limit=200
            )
            await self.update_chart(df)

            # 生成信号
            signals = self.strategy.generate_signals(df)
            self.update_signals_table(signals[-10:])  # 显示最近10个信号

        except Exception as e:
            ui.notify(f'数据获取失败: {str(e)}', type='negative')

    def update_signals_table(self, signals: List[Signal]):
        """更新信号表格"""
        if self.signals_table is None:
            return

        rows = []
        for signal in signals:
            rows.append({
                'time': signal.timestamp.strftime('%H:%M:%S'),
                'signal': signal.signal_type,
                'price': f'{signal.price:.4f}',
                'confidence': f'{signal.confidence:.2%}'
            })

        self.signals_table.rows = rows

    async def toggle_monitoring(self):
        """切换实时监控状态"""
        # 实现实时监控逻辑
        pass

# 应用启动
def main():
    dashboard = TradingDashboard()

    @ui.page('/')
    def index():
        dashboard.create_main_layout()
        dashboard.create_chart_area()

        # 初始化数据
        ui.timer(1.0, dashboard.refresh_data, once=True)

    ui.run(title='炒币策略交易提示系统', port=8080, show=False)

if __name__ == '__main__':
    main()
```

## 项目文件结构
```
crypto_trading_system/
├── main.py                 # 主程序入口
├── requirements.txt        # 依赖包列表
├── config/
│   ├── __init__.py
│   ├── settings.py        # 配置文件
│   └── api_keys.py        # API密钥配置
├── core/
│   ├── __init__.py
│   ├── data_provider.py   # 数据获取模块
│   ├── strategy_engine.py # 策略引擎
│   ├── backtest_engine.py # 回测引擎
│   └── paper_trading.py   # 模拟交易
├── strategies/
│   ├── __init__.py
│   ├── base_strategy.py   # 策略基类
│   ├── ma_cross.py        # 均线交叉策略
│   ├── rsi_strategy.py    # RSI策略
│   └── multi_indicator.py # 多指标组合策略
├── ui/
│   ├── __init__.py
│   ├── dashboard.py       # 主界面
│   ├── components.py      # UI组件
│   └── charts.py          # 图表组件
├── utils/
│   ├── __init__.py
│   ├── database.py        # 数据库操作
│   ├── indicators.py      # 自定义指标
│   └── helpers.py         # 辅助函数
├── tests/
│   ├── __init__.py
│   ├── test_strategies.py
│   ├── test_data_provider.py
│   └── test_backtest.py
├── data/                  # 数据存储目录
├── logs/                  # 日志文件目录
└── docs/                  # 文档目录
```

**注意**: 本计划基于当前技术调研结果制定，具体实施过程中可能需要根据实际情况进行调整。
